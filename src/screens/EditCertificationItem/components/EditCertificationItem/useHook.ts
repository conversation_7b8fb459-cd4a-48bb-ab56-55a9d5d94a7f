import { useEffect, useState } from 'react';
import { Linking } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { pick, types } from '@react-native-documents/picker';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectMultipleSelectionsByKey } from '@/src/redux/selectors/search';
import {
  clearMultipleSelections,
  clearSelection,
} from '@/src/redux/slices/entitysearch/searchSlice';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { compressToTargetSize } from '@/src/utilities/upload/compress';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import { IdNameI, IdTitle<PERSON>, IdType<PERSON> } from '@/src/types/common/data';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import {
  addCertificationAPI,
  editCertificationAPI,
  fetchCertificationAPI,
} from '@/src/networks/career/certification';
import { EditCertificationBodyI } from '@/src/networks/career/types';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';
import { CertificationFormDataI, CompressedFileI, UseEditCertificationItemI } from './types';

export const useEditCertificationItem = (
  profileId?: string,
  certificationId?: string,
): UseEditCertificationItemI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [accessUrl, setAccessUrl] = useState<string | null>(null);
  const [localSkills, setLocalSkills] = useState<SearchResultI[]>([]);
  const skillsSelection = useSelector(selectMultipleSelectionsByKey('skill'));
  const [initialSkills, setInitialSkills] = useState<SearchResultI[]>([]);
  const [downloadUrl, setDownloadUrl] = useState<string | undefined>();
  const [isFileRemoved, setIsFileRemoved] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isPresent, setIsPresent] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const dispatch = useDispatch();

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const MAX_FILE_SIZE = 5 * 1024 * 1024;

  const typeOptions: IdTitleI[] = [
    { title: 'Statutory', id: 'statutory' },
    { title: 'Value Added', id: 'value-added' },
  ];

  const methods = useForm<CertificationFormDataI>({
    mode: 'onChange',
    defaultValues: {
      type: 'statutory',
      course: {},
      provider: {},
      validFrom: '',
      validUntil: '',
      documentUrl: '',
      skills: [],
    },
  });

  useEffect(() => {
    if (certificationId) {
      const fetchCertification = async () => {
        try {
          setLoading(true);
          const response = await fetchCertificationAPI(certificationId);
          const normalizedType = normalizeCertificateType(response.certificateCourse.type);
          const fetchedCertification = {
            type: normalizedType,
            course: response.certificateCourse,
            provider: response.entity,
            validFrom: response.fromDate,
            validUntil: response.untilDate,
            skills: response.skills as unknown as IdNameI[],
            ...(response?.fileUrl ? { documentUrl: response.fileUrl } : {}),
          };

          if (!response.untilDate) {
            setIsPresent(true);
          }

          setLocalSkills((response.skills as unknown as SearchResultI[]) || []);
          setInitialSkills(response.skills as unknown as SearchResultI[]);
          methods.reset(fetchedCertification);
          methods.setValue('type', normalizedType);

          if (response.fileUrl) {
            setDownloadUrl(response.fileUrl);
          }
        } catch (err) {
          handleError(err);
          triggerErrorBoundary(
            new Error(
              'Failed to load certification: ' +
                (err instanceof Error ? err.message : 'Unknown error'),
            ),
          );
        } finally {
          setLoading(false);
        }
      };
      fetchCertification();
    }
  }, [certificationId, methods]);

  useEffect(() => {
    if (!skillsSelection) return;
    setLocalSkills((prev) => {
      const existingIds = new Set(prev.map((s) => s.id));
      const merged = [...prev, ...skillsSelection.filter((s) => !existingIds.has(s.id))];
      return merged;
    });
  }, [skillsSelection]);

  const onSubmit = async (data: CertificationFormDataI) => {
    try {
      setIsSubmitting(true);
      const payload = transformCertificationDataForCreate(data, localSkills, accessUrl);

      console.log(payload);

      if (certificationId) {
        const deletedSkills = initialSkills
          .filter((initial) => !localSkills.some((local) => local.id === initial.id))
          .map(({ id, dataType }) => ({ id, dataType }));

        const addedSkills = localSkills
          .filter((local) => !initialSkills.some((initial) => initial.id === local.id))
          .map(({ id, dataType }) => ({ id, dataType }));

        const payload = transformCertificationDataForEdit(
          data,
          addedSkills,
          deletedSkills,
          accessUrl,
          downloadUrl,
          isFileRemoved,
        );

        await editCertificationAPI(certificationId, payload);
      } else {
        const response = await addCertificationAPI(payload);
      }

      showToast({
        message: 'Success',
        description: `Added new Certification successfully`,
        type: 'success',
      });
      navigation.goBack();
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Save Certification',
            description: 'Unable to save certification',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAttachment = async () => {
    try {
      const [result] = await pick({
        type: [types.pdf, types.images],
      });

      if (!result) {
        showToast({
          message: 'No file selected',
          type: 'error',
        });
        return;
      }

      const allowedMimeTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];
      const isAllowedType = allowedMimeTypes.includes(result.type ?? '');

      if (!isAllowedType) {
        showToast({
          message: 'Unsupported file type',
          description: 'Only PDF, JPG, and PNG files are allowed.',
          type: 'error',
        });
        return;
      }

      setSelectedFile(result.name);

      let compressedFile: CompressedFileI = {
        uri: result.uri,
        type: result.type ?? 'application/pdf',
        filename: result.name ?? `File-${result.size}.jpg`,
      };

      const isImage = result.type !== 'application/pdf';
      if (isImage) {
        const compressedUri = await compressToTargetSize(result.uri, 500);
        compressedFile = {
          uri: compressedUri,
          type: 'image/jpeg',
          filename: compressedFile.filename,
        };
      } else {
        if ((result.size ?? 0) > MAX_FILE_SIZE) {
          showToast({
            message: 'File too large',
            description: `Maximum file size is ${MAX_FILE_SIZE / (1024 * 1024)}MB.`,
            type: 'error',
          });
          return;
        }
      }

      const extension = compressedFile.type.split('/')[1];
      const [uploadDetails] = await fetchPresignedUrlAPI([extension], 'CERTIFICATION');
      await uploadFileWithPresignedUrl(compressedFile, uploadDetails.uploadUrl);
      setAccessUrl(uploadDetails.accessUrl);
      setIsFileRemoved(false);
    } catch (error) {
      showToast({
        message: 'Upload Failed',
        description: 'Media selection failed or upload was cancelled.',
        type: 'error',
      });
    }
  };

  const handleDownload = async () => {
    try {
      if (!downloadUrl) {
        showToast({
          message: 'Error',
          description: 'No File to Download',
          type: 'error',
        });
        return;
      }

      await Linking.openURL(downloadUrl);
      showToast({
        message: 'Success',
        description: 'Download Started',
        type: 'success',
      });
    } catch (error) {
      showToast({
        message: 'Error',
        description: 'Download Failed',
        type: 'error',
      });
    }
  };

  const handleRemoveFile = () => {
    if (selectedFile) {
      setSelectedFile(null);
      setAccessUrl(null);
      showToast({
        message: 'File Removed',
        description: 'The selected file has been removed',
        type: 'success',
      });
    } else if (downloadUrl) {
      setIsFileRemoved(true);
      showToast({
        message: 'File Marked for Removal',
        description: 'The file will be removed when you save the certification',
        type: 'success',
      });
    }
  };

  const clearFields = () => {
    dispatch(clearMultipleSelections('skill'));
    dispatch(clearSelection('entity'));
    dispatch(clearSelection('certificate-course'));
  };

  const handlePresentCheckbox = () => {
    const newPresentState = !isPresent;
    setIsPresent(newPresentState);
    if (newPresentState) {
      methods.setValue('validUntil', null);
    }
  };

  return {
    methods,
    typeOptions,
    isSubmitting,
    onSubmit,
    navigation,
    selectedFile,
    handleAttachment,
    localSkills,
    setLocalSkills,
    loading,
    handleDownload,
    handleRemoveFile,
    isFileRemoved,
    clearFields,
    isPresent,
    handlePresentCheckbox,
    isSubmitted,
    setIsSubmitted,
  };
};

const transformCertificationDataForCreate = (
  data: CertificationFormDataI,
  skills: SearchResultI[],
  fileUrl: string | null,
) => {
  const payload = {
    institute: {
      id: data.provider.id,
      dataType: data.provider.dataType,
    },
    certificateCourse: {
      id: data.course.id,
      dataType: data.course.dataType,
    },
    fromDate: data.validFrom,
    untilDate: data.validUntil,
    ...(fileUrl && { fileUrl }),
    skills: skills.map((skill) => {
      return {
        id: skill.id,
        dataType: skill.dataType,
      };
    }),
  };
  return payload;
};

const normalizeCertificateType = (type: string): string => {
  return type.toLowerCase().replace('_', '-');
};

const transformCertificationDataForEdit = (
  data: CertificationFormDataI,
  addedSkills: IdTypeI[],
  deletedSkills: IdTypeI[],
  fileUrl: string | null,
  existingFileUrl?: string,
  isFileRemoved?: boolean,
) => {
  const payload = {
    institute: {
      id: data.provider.id,
      dataType: data.provider.dataType,
    },
    certificateCourse: {
      id: data.course.id,
      dataType: data.course.dataType,
    },
    fromDate: data.validFrom,
    untilDate: data.validUntil,
    skillsToAdd: addedSkills.map((skill) => {
      return {
        id: skill.id,
        dataType: skill.dataType,
      };
    }),
    skillsToDelete: deletedSkills.map((skill) => {
      return {
        id: skill.id,
        dataType: skill.dataType,
      };
    }),
  };

  if (isFileRemoved && existingFileUrl) {
    (payload as EditCertificationBodyI).file = {
      opr: 'DELETE',
    };
  } else if (fileUrl) {
    (payload as EditCertificationBodyI).file = {
      opr: existingFileUrl ? 'UPDATE' : 'CREATE',
      fileUrl: fileUrl,
    };
  }

  return payload;
};
