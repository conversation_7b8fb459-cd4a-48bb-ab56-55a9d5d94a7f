import { KeyboardAvoidingView, Platform, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import SafeArea from '@/src/components/SafeArea';
import CreateCommunityForm from './components/CreateCommunityForm';
import CreateCommunityHeader from './components/CreateCommunityHeader';
import useCreateCommunityForm from './components/CreateCommunityForm/useHook';

const CreateCommunityScreen = () => {
  const { methods, handleNext } = useCreateCommunityForm();

  return (
    <SafeArea>
      <KeyboardAvoidingView
        {...(Platform.OS === 'ios' ? { behavior: 'padding' } : {})}
        style={{ flex: 1 }}
      >
        <ScrollView contentContainerStyle={{ flexGrow: 1 }} keyboardShouldPersistTaps="always">
          <View className="flex-1 px-5">
            <CreateCommunityHeader currentPage={1} onNext={() => handleNext()} />
            <CreateCommunityForm methods={methods} />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeArea>
  );
};

export default CreateCommunityScreen;
