/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { Pressable, ScrollView, Text, TouchableWithoutFeedback, View } from 'react-native';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import { twMerge } from 'tailwind-merge';
import ChevronDown from '@/src/assets/svgs/ChevronDown';
import { IdTitleI, SelectHandleI, SelectProps } from './types';

const Select = forwardRef<SelectHandleI, SelectProps>(
  (
    {
      options,
      value,
      onChange,
      placeholder = 'Select an option',
      label,
      error,
      className,
      dropdownClassName,
      labelClassName,
      errorClassName,
      disabled = false,
    },
    ref,
  ) => {
    const [isOpen, setIsOpen] = useState(false);
    const [dropdownLayout, setDropdownLayout] = useState({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    });
    const dropdownRef = useRef<View>(null);
    const rotation = useSharedValue(0);

    const selectedOption = options.find((option) => option.id === value || option.title === value);

    const toggle = () => {
      if (disabled) return;

      if (!isOpen) {
        dropdownRef.current?.measureInWindow((x, y, width, height) => {
          setDropdownLayout({
            x,
            y,
            width,
            height,
          });
          setIsOpen(true);
          rotation.value = withTiming(180, { duration: 200 });
        });
      } else {
        close();
      }
    };

    const close = () => {
      setIsOpen(false);
      rotation.value = withTiming(0, { duration: 200 });
    };

    useImperativeHandle(
      ref,
      () => ({
        toggle,
        close,
      }),
      [],
    );

    const handleOptionClick = (option: IdTitleI) => {
      onChange(typeof value === 'string' ? option.title : option.id.toString());
      close();
    };

    const animatedStyles = useAnimatedStyle(() => ({
      transform: [{ rotate: `${rotation.value}deg` }],
    }));

    const handleOutsideClick = (event: { nativeEvent: { pageX: number; pageY: number } }) => {
      if (isOpen) {
        const { pageX, pageY } = event.nativeEvent;
        if (
          pageX >= dropdownLayout.x &&
          pageX <= dropdownLayout.x + dropdownLayout.width &&
          pageY >= dropdownLayout.y &&
          pageY <= dropdownLayout.y + dropdownLayout.height
        ) {
          return;
        }

        const dropdownTop = dropdownLayout.y + dropdownLayout.height + 1;
        const dropdownMaxHeight = 240;
        if (
          pageX >= dropdownLayout.x &&
          pageX <= dropdownLayout.x + dropdownLayout.width &&
          pageY >= dropdownTop &&
          pageY <= dropdownTop + dropdownMaxHeight
        ) {
          return;
        }

        close();
      }
    };

    return (
      <View className={twMerge('my-1', className)}>
        {label && (
          <Text className={twMerge('mb-1 text-sm text-neutral-700', labelClassName)}>{label}</Text>
        )}
        <View className="relative" ref={dropdownRef}>
          <Pressable
            onPress={toggle}
            disabled={disabled}
            className={twMerge(
              'w-full px-4 py-4 rounded-xl border border-[#D4D4D4] bg-white',
              error && 'border-red-500',
              disabled && 'bg-gray-100 opacity-50',
              dropdownClassName,
            )}
          >
            <View className="flex-row justify-between items-center">
              <Text
                className={twMerge(
                  'text-neutral-900',
                  !selectedOption && 'text-gray-500',
                  disabled && 'text-gray-400',
                )}
              >
                {selectedOption ? selectedOption.title : placeholder}
              </Text>
              <Animated.View style={animatedStyles}>
                <ChevronDown />
              </Animated.View>
            </View>
          </Pressable>
          {isOpen && (
            <View
              className="absolute top-full left-0 right-0 mt-1 bg-white rounded-md shadow-lg z-50"
              style={{
                elevation: 5,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.25,
                shadowRadius: 3.84,
              }}
            >
              <ScrollView className="max-h-60" showsVerticalScrollIndicator={false} bounces={false}>
                {options.map((option) => (
                  <Pressable
                    key={typeof option.id === 'number' ? option.id.toString() : option.id}
                    className={twMerge(
                      'px-4 py-3',
                      (option.id === value || option.title === value) && 'bg-neutral-100',
                    )}
                    android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
                    onPress={() => handleOptionClick(option)}
                  >
                    <Text className="text-neutral-900">{option.title}</Text>
                  </Pressable>
                ))}
              </ScrollView>
            </View>
          )}
        </View>
        {error && (
          <Text className={twMerge('text-sm text-red-500 mt-1', errorClassName)}>{error}</Text>
        )}
        {isOpen && (
          <TouchableWithoutFeedback onPress={handleOutsideClick}>
            <View
              style={{
                position: 'absolute',
                top: -5000,
                left: -5000,
                width: 10000,
                height: 10000,
                zIndex: 40,
              }}
            />
          </TouchableWithoutFeedback>
        )}
      </View>
    );
  },
);

Select.displayName = 'Select';

export default Select;
